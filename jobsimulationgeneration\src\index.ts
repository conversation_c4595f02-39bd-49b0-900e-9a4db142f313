import dotenv from 'dotenv';
import path from 'path';
import app from './app';

dotenv.config({ path: path.resolve(process.cwd(), '.env') });

const PORT = process.env.PORT || 3002;

console.log("::: CHECK ENV ::: ", process.env.PORT);

app.listen(PORT, () => {
  console.log(`🚀 Job Simulation Generation Engine is running on port ${PORT}`);
  console.log(`📋 Health check available at: http://localhost:${PORT}/health`);
  console.log(`🌐 API documentation at: http://localhost:${PORT}/`);
});
