{"name": "jobsimulationgeneration", "version": "1.0.0", "description": "Simulation Generation Engine", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "nodemon --exec tsx src/index.ts", "clean": "<PERSON><PERSON><PERSON> dist", "prebuild": "npm run clean", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["nodejs", "express", "typescript", "simulation", "api"], "author": "", "license": "ISC", "dependencies": {"cors": "^2.8.5", "express": "^5.1.0", "dotenv": "^16.4.7"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/node": "^24.1.0", "nodemon": "^3.1.10", "rimraf": "^6.0.1", "tsx": "^4.19.2", "typescript": "^5.9.2"}}