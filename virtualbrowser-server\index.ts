import { openai } from "@ai-sdk/openai";
import { Browserbase } from "@browserbasehq/sdk";
import { ObserveResult, Stagehand } from "@browserbasehq/stagehand";
import { CoreMessage, generateObject, LanguageModelV1, UserContent } from "ai";
import dotenv from "dotenv";
import path from "path";
import sharp from 'sharp';
import { fileURLToPath } from "url";
import uWS from 'uWebSockets.js';
import { z } from "zod";
import StagehandConfig from "./stagehand.config.js";
import { FRAME_RATE, IMAGE_QUALITY, IMAGE_WIDTH } from "./types/constant.js";
import { BrowserClientEvent, SocketUserData, Step } from "./types/index.js";
import { getClosestRegion } from "./utils/regionSelector.js";
import runStagehand from "./utils/runStagehand.js";

// Get current directory in ES module
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

dotenv.config({ path: path.resolve(__dirname, '.env') });

const LLMClient = openai("gpt-4o-mini");
const isLocalBrowser = process.env.STAGEHAND_ENV === 'LOCAL';
const PORT = process.env.WS_PORT || 3001;

const MapSocketUser: Map<string, { socket: uWS.WebSocket<SocketUserData>, stagehand: Stagehand | null, screenshotInterval: NodeJS.Timeout | null }> = new Map();

const browserbase = isLocalBrowser ? null : new Browserbase({ apiKey: process.env.BROWSERBASE_API_KEY! });

// Screenshot service with sharp optimization
async function startScreenshotCapture(ws: uWS.WebSocket<SocketUserData>) {
    const socketUser = MapSocketUser.get(ws.getUserData().userId);
    const userStagehand = socketUser?.stagehand;
    if (!userStagehand || !userStagehand.page) {
        console.log('No active Stagehand instance for screenshot');
        return;
    }

    const captureScreenshot = async () => {
        try {
            if (!userStagehand || !userStagehand.page) {
                console.log("Stagehand instance lost, stopping screenshot capture");
                stopScreenshotCapture(ws.getUserData().userId);
                sendStatus(ws, "Browser crashed - restart required");
                return;
            }

            // Capture screenshot with Stagehand
            const screenshot = await userStagehand.page.screenshot({
                type: 'jpeg',
                quality: 80,
                fullPage: false
            });

            // Optimize with sharp
            const optimizedBuffer = await sharp(screenshot)
                .resize({ width: IMAGE_WIDTH })
                .jpeg({ quality: IMAGE_QUALITY, progressive: true })
                .toBuffer();

            ws.send(optimizedBuffer, true);

            // Schedule next screenshot
            socketUser.screenshotInterval = setTimeout(captureScreenshot, FRAME_RATE);

        } catch (error) {
            console.error('Error capturing screenshot:', error);
            // Continue capturing on image compression failure, just skip this frame
            if (error instanceof Error && (error.message?.includes('sharp') || error.message?.includes('compression'))) {
                console.log('Image compression failed, skipping frame');
                socketUser.screenshotInterval = setTimeout(captureScreenshot, FRAME_RATE);
            } else {
                // Other errors might indicate browser crash
                stopScreenshotCapture(ws.getUserData().userId);
                sendStatus(ws, 'Screenshot capture failed - browser may have crashed');
            }
        }
    };

    // Start first capture
    captureScreenshot();
}

function stopScreenshotCapture(userId: string) {
    const userData = MapSocketUser.get(userId);
    if (userData?.screenshotInterval) {
        clearTimeout(userData.screenshotInterval);
        userData.screenshotInterval = null;
        console.log('Screenshot capture stopped for user ::: ', userId);
    }
}

// TODO: dont broadcast status, send to connecting client only
function sendStatus(ws: uWS.WebSocket<SocketUserData>, message: string, code?: string) {
    const statusMessage = JSON.stringify({
        type: 'status',
        code,
        message,
        timestamp: new Date().toISOString()
    });

    try {
        ws.send(statusMessage, false); // false = text
    } catch (error) {
        console.error('Error sending status:', error);
    }

}

function sendAgentMessage(ws: uWS.WebSocket<SocketUserData>, message: string) {
    const agentMessage = JSON.stringify({
        type: 'agent_message',
        message,
        timestamp: new Date().toISOString()
    });

    try {
        ws.send(agentMessage, false);
    } catch (error) {
        console.error('Error sending agent message:', error);
    }

}

function sendUrl(ws: uWS.WebSocket<SocketUserData>, url: string) {
    const urlMessage = JSON.stringify({
        type: 'url',
        url,
        timestamp: new Date().toISOString()
    });

    try {
        ws.send(urlMessage, false); // false = text
    } catch (error) {
        console.error('Error sending URL:', error);
    }
}

function sendLiveViewLink(ws: uWS.WebSocket<SocketUserData>, url: string) {
    const urlMessage = JSON.stringify({
        type: 'live_view_link',
        url,
        timestamp: new Date().toISOString()
    });

    try {
        ws.send(urlMessage, false);
    } catch (error) {
        console.error('Error sending URL:', error);
    }
}

function sendBrowserEnv(ws: uWS.WebSocket<SocketUserData>, browserEnv: string) {
    const urlMessage = JSON.stringify({
        type: 'browser_env',
        env: browserEnv,
        timestamp: new Date().toISOString()
    });

    try {
        ws.send(urlMessage, false);
    } catch (error) {
        console.error('Error sending URL:', error);
    }
}

// Handle click on browser
async function handleClick(ws: uWS.WebSocket<SocketUserData>, x: number, y: number) {
    try {
        const userStagehand = MapSocketUser.get(ws.getUserData().userId)?.stagehand;
        if (!userStagehand || !userStagehand.page) {
            sendStatus(ws, 'No active browser instance');
            return;
        }

        // Click on the page
        await userStagehand.page.mouse.click(x, y);
        console.log(`Clicked at coordinates: ${x}, ${y}`);

    } catch (error) {
        console.error('Click error:', error);
        sendStatus(ws, 'Click failed: ' + (error instanceof Error ? error.message : 'Unknown error'));
    }
}

const sendSteps = (ws: uWS.WebSocket<SocketUserData>, data: { action: string, result: Step, steps: Step[], done: boolean, error?: string }) => {
    const stepMessage = JSON.stringify({
        type: 'goal_step',
        data
    });

    try {
        ws.send(stepMessage, false);
    } catch (error) {
        console.error('Error sending step:', error);
    }
}

const sendPrompt = async ({
    stagehand,
    goal,
    previousSteps = [],
    previousExtraction,
}: {
    stagehand: Stagehand;
    goal: string;
    previousSteps?: Step[];
    previousExtraction?: string | ObserveResult[];
}) => {
    const currentUrl = stagehand.page.url();

    const content: UserContent = [
        {
            type: "text",
            text: `Consider the following screenshot of a web page${currentUrl ? ` (URL: ${currentUrl})` : ""
                }, with the goal being "${goal}".
${previousSteps.length > 0
                    ? `Previous steps taken:
${previousSteps
                        .map(
                            (step, index) => `
Step ${index + 1}:
- Action: ${step.text}
- Reasoning: ${step.reasoning}
- Tool Used: ${step.tool}
- Instruction: ${step.instruction}
`
                        )
                        .join("\n")}`
                    : ""
                }
Determine the immediate next step to take to achieve the goal.

Important guidelines:
1. Break down complex actions into individual atomic steps
2. For ACT commands, use only one action at a time, such as:
   - Single click on a specific element
   - Type into a single input field
   - Select a single option
3. Avoid combining multiple actions in one instruction
4. If multiple actions are needed, they should be separate steps

If the goal has been achieved, return "close".`,
        },
    ];

    // Add screenshot if navigated to a page previously
    if (
        previousSteps.length > 0 &&
        previousSteps.some((step) => step.tool === "GOTO")
    ) {
        const screenshot = await stagehand.page.screenshot({
            type: 'jpeg',
            quality: 80,
            fullPage: false
        });
        const image = `data:image/jpeg;base64,${screenshot.toString('base64')}`;
        content.push({
            type: "image",
            image: image,
        });
    }

    if (previousExtraction) {
        content.push({
            type: "text",
            text: `The result of the previous ${Array.isArray(previousExtraction) ? "observation" : "extraction"
                } is: ${previousExtraction}.`,
        });
    }

    const message: CoreMessage = {
        role: "user",
        content,
    };

    const result = await generateObject({
        model: LLMClient as LanguageModelV1,
        schema: z.object({
            text: z.string(),
            reasoning: z.string(),
            tool: z.enum([
                "GOTO",
                "ACT",
                "EXTRACT",
                "OBSERVE",
                "CLOSE",
                "WAIT",
                "NAVBACK",
            ]),
            instruction: z.string(),
        }),
        messages: [message],
    });

    return {
        result: result.object,
        steps: [...previousSteps, result.object],
    };
}

const handleGoal = async (ws: uWS.WebSocket<SocketUserData>, goalEvent: BrowserClientEvent) => {
    if (goalEvent.type !== 'goal') return;
    const { action, goal, previousSteps, step } = goalEvent.data;
    const stagehand = MapSocketUser.get(ws.getUserData().userId)!.stagehand!;
    if (!stagehand) {
        // TODO: send error
        return;
    }
    switch (action) {
        // case 'START': {
        //     // TODO Break down task and send
        //     const data = await sendPrompt({
        //         stagehand: MapSocketUser.get(ws.getUserData().userId)!.stagehand!,
        //         goal,
        //         previousSteps,
        //     });
        //     sendSteps(ws, {
        //         result: data.result,
        //         steps: data.previousSteps,
        //         done: data.result.tool === "CLOSE",
        //     });
        //     break;
        // }
        case 'GET_NEXT_STEP': {
            // TODO Break down task and send
            const data = await sendPrompt({
                stagehand: MapSocketUser.get(ws.getUserData().userId)!.stagehand!,
                goal,
                previousSteps,
            });
            sendSteps(ws, {
                action,
                result: data.result,
                steps: data.steps,
                done: data.result.tool === "CLOSE",
            });
            break;
        }
        case 'EXECUTE_STEP': {
            if (!step) return;

            const result = await runStagehand({
                stagehand,
                method: step.tool,
                instruction: step.instruction,
            });

            sendSteps(ws, {
                action,
                result: step,
                steps: [],
                done: step.tool === "CLOSE"
            });

            break;
        }
    }
}

// Initialize WebSocket server
const app = uWS.App()
    .ws<SocketUserData>('/ws', {
        upgrade: (res, req, context) => {
            console.log('An Http connection wants to become WebSocket, URL: ' + req.getUrl() + '!');
            const query = req.getQuery();
            const urlSearchParams = new URLSearchParams(query);
            let userData: SocketUserData = {
                userId: urlSearchParams.get('userId') || Date.now().toString(),
                userName: urlSearchParams.get('userName') || 'Anonymous',
            };

            /* This immediately calls open handler, you must not use res after this call */
            res.upgrade(
                userData,
                /* Spell these correctly */
                req.getHeader('sec-websocket-key'),
                req.getHeader('sec-websocket-protocol'),
                req.getHeader('sec-websocket-extensions'),
                context
            );
        },
        open: async (ws) => {
            console.log('Client connected ', ws.getUserData().userId);

            MapSocketUser.set(ws.getUserData().userId, {
                socket: ws,
                stagehand: null,
                screenshotInterval: null,
            });

            const userData = ws.getUserData();

            // Send connection confirmation
            ws.send('Connected to WebSocket server');

            // Send current status
            const userStagehand = MapSocketUser.get(userData.userId)?.stagehand;
            const status = !!userStagehand ? 'Browser active' : 'No browser instance';
            ws.send(JSON.stringify({
                type: 'status',
                message: status,
                timestamp: new Date().toISOString()
            }));
        },

        message: async (ws: uWS.WebSocket<SocketUserData>, message: ArrayBuffer, isBinary: boolean) => {
            if (isBinary) return;
            try {
                const browserClientEvent: BrowserClientEvent = JSON.parse(Buffer.from(message).toString());
                let userStagehand = MapSocketUser.get(ws.getUserData().userId)?.stagehand;

                // User provide url and
                if (browserClientEvent.type === 'start_browser') {
                    console.log("START BROWSER ::: ", browserClientEvent, "\n");
                    let useLocalBrowser = isLocalBrowser;

                    if (!userStagehand) {
                        try {
                            sendStatus(ws, 'Initializing browser...', 'initializing');

                            // @ts-ignore
                            const initStagehand = new Stagehand({
                                ...StagehandConfig,
                                ...(isLocalBrowser ? {} : {
                                    browserbaseSessionCreateParams: {
                                        ...(StagehandConfig.browserbaseSessionCreateParams),
                                        region: getClosestRegion(browserClientEvent.data?.timezone)
                                    }
                                }),

                            });
                            await initStagehand.init();

                            MapSocketUser.get(ws.getUserData().userId)!.stagehand = initStagehand;
                            userStagehand = initStagehand;

                            sendBrowserEnv(ws, process.env.STAGEHAND_ENV!);

                        } catch (error) {
                            console.error('Failed to initialize:', error);
                            sendStatus(ws, 'Failed to initialize: ' + (error instanceof Error ? error.message : 'Unknown error'));
                        }
                    }
                    // Fallback to local browser if browserbase failed
                    if (!isLocalBrowser && !userStagehand) {
                        try {
                            const initStagehand = new Stagehand({
                                ...StagehandConfig,
                                env: 'LOCAL',
                            });
                            await initStagehand.init();

                            MapSocketUser.get(ws.getUserData().userId)!.stagehand = initStagehand;
                            userStagehand = initStagehand;

                            useLocalBrowser = true;
                            sendBrowserEnv(ws, 'LOCAL');
                        } catch (error) {
                            sendStatus(ws, 'Failed to initialize local browser (fallback): ' + (error instanceof Error ? error.message : 'Unknown error'));
                        }
                    }
                    const navigatingUrl = browserClientEvent.data.url || "https://google.com";
                    const navigatingStep: Step = {
                        text: `Navigating to ${navigatingUrl}`,
                        reasoning: `Navigating to ${navigatingUrl}`,
                        tool: 'GOTO',
                        instruction: navigatingUrl,
                    };
                    if (userStagehand) {
                        await userStagehand.page.goto(navigatingUrl, { waitUntil: 'commit', timeout: 60000 });
                        if (browserbase && userStagehand.browserbaseSessionID) {
                            const liveViewLinks = await browserbase.sessions.debug(userStagehand.browserbaseSessionID!);
                            const liveViewLink = liveViewLinks.debuggerFullscreenUrl;
                            sendLiveViewLink(ws, liveViewLink);
                        }
                        sendUrl(ws, userStagehand.page.url());
                        sendSteps(ws, {
                            action: 'START',
                            result: navigatingStep,
                            steps: [navigatingStep],
                            done: false,
                        });
                        sendStatus(ws, 'Running...', 'running');

                        if (!browserbase || useLocalBrowser) {
                            // Start screenshot capture
                            startScreenshotCapture(ws);
                        }
                    } else {
                        sendSteps(ws, {
                            action: 'START',
                            result: navigatingStep,
                            steps: [navigatingStep],
                            done: true,
                            error: "Unknown error",
                        });
                    }
                    return;
                }

                if (browserClientEvent.type === 'agent_instruction') {
                    if (!userStagehand) {
                        sendStatus(ws, 'No active browser instance');
                        return;
                    }
                    const agent = userStagehand.agent({
                        provider: "openai",
                        model: "computer-use-preview",
                    });

                    const result = await agent.execute({
                        instruction: browserClientEvent.data.instruction,
                        autoScreenshot: false,
                    });

                    if (result.message) {
                        sendAgentMessage(ws, result.message);
                    }

                    console.log("instruction agent result ::: ", result);
                    return;
                }

                if (browserClientEvent.type === 'goal') {
                    handleGoal(ws, browserClientEvent);
                    return;
                }

                if (browserClientEvent.type === 'click') {
                    await handleClick(ws, browserClientEvent.data.x, browserClientEvent.data.y);

                    return;
                }

                if (browserClientEvent.type === 'close_browser') {
                    if (userStagehand) {
                        stopScreenshotCapture(ws.getUserData().userId);
                        try {
                            await userStagehand.close();
                        } catch (error) {

                        }
                        MapSocketUser.set(ws.getUserData().userId, {
                            socket: ws,
                            stagehand: null,
                            screenshotInterval: null,
                        });
                        sendStatus(ws, 'Browser closed');
                    } else {
                        sendStatus(ws, 'No active browser instance to close');
                    }
                    return;
                }

                if (browserClientEvent.type === 'reload') {
                    if (userStagehand) {
                        await userStagehand.page.reload();
                    }
                    return;
                }
                if (browserClientEvent.type === 'scroll') {
                    if (userStagehand) {
                        let deltaY = browserClientEvent.data.direction === 'up' ? -150 : 150;
                        await userStagehand.page.mouse.wheel(0, deltaY);
                    }
                    return;
                }
            } catch (error) {
                console.error('Message parsing error:', error);
            }
        },

        close: (ws: uWS.WebSocket<SocketUserData>) => {
            console.log('Client disconnected ', ws.getUserData().userId);
            // TODO: should we wait for reconnect here(?)
            try {
                MapSocketUser.get(ws.getUserData().userId)?.stagehand?.close().catch((error) => {
                    console.error('Error closing stagehand:', error);
                });
            } catch (error) { }
            stopScreenshotCapture(ws.getUserData().userId);
            MapSocketUser.delete(ws.getUserData().userId);
        },
    })
    .any('/health', (res: uWS.HttpResponse, req: uWS.HttpRequest) => {
        res.writeHeader('Content-Type', 'application/json');
        res.end(JSON.stringify({ status: 'OK', timestamp: new Date().toISOString(), }));
    })
    .any('/*', (res: uWS.HttpResponse, req: uWS.HttpRequest) => {
        res.end('WebSocket server running on /ws');
    })
    .listen(Number(PORT), (token: any) => {
        if (token) {
            console.log(`🚀 uWebSockets.js server running on ws://localhost:${PORT}/ws`);
            console.log(`🚀 Using ${process.env.STAGEHAND_ENV} browser`);
        } else {
            console.error('Failed to listen to port', PORT);
        }
    });

// Graceful shutdown
process.on('SIGINT', async () => {
    console.log('\n🛑 Shutting down...');

    if (MapSocketUser.size > 0) {
        Promise.all(Array.from(MapSocketUser.values()).map(user => user.stagehand?.close()));
    }

    process.exit(0);
});

export default app;
