# Job Simulation Generation Engine

A Node.js Express TypeScript application for generating job simulations.

## Features

- ✅ Express.js server with TypeScript
- ✅ CORS enabled for cross-origin requests
- ✅ Health check endpoint
- ✅ Error handling middleware
- ✅ Development and production scripts
- ✅ Hot reload with nodemon

## Installation

```bash
npm install
```

## Development

Start the development server with hot reload:

```bash
npm run dev
```

Or run with ts-node directly:

```bash
npm run dev:ts
```

## Production

Build the project:

```bash
npm run build
```

Start the production server:

```bash
npm start
```

## API Endpoints

### Health Check
- **GET** `/health` - Returns server health status
- **GET** `/` - Returns API information and available endpoints

## Project Structure

```
src/
├── app.ts      # Express application setup
└── server.ts   # Server entry point
```

## Environment Variables

- `PORT` - Server port (default: 3000)

## Scripts

- `npm run build` - Build TypeScript to JavaScript
- `npm run start` - Start production server
- `npm run dev` - Start development server with hot reload
- `npm run dev:ts` - Start development server with ts-node
- `npm run clean` - Clean build directory